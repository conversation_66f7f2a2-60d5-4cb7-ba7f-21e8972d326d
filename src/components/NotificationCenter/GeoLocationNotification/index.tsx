
'use client'
import { usePathname } from 'next/navigation'
import { useEffect, useId } from 'react'
import {
  CONTINENT_REGION_MAP,
  REGION_TO_SLUG_MAP,
  getSessionStorageItem,
} from '../../../globals/utils'
import { GEO_NOTIFICATION_SLUGS } from '../../../lib/constant'
import { setGeoLocationData } from '../../../redux/slices/appSlice'
import { setLocalPushNotifications } from '../../../redux/slices/notificationCenterSlice'
import store, { useAppDispatch, useAppSelector } from '../../../redux/store'
import { getCountryName } from '../../PushNotifications/helpers'

const SLUG_REGION_MAP: Record<string, 'APAC' | 'EMEA' | 'AMER'> = {
  au: 'APAC',
  uk: 'EMEA',
  us: 'AMER',
}
function GeoLocationNotification() {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const geolocationData = useAppSelector(
    (state) => state.app.geoLocationData
  )
  const uuid = useId()

  const fetchGeoLocation = async () => {
    const pathSegments = pathname?.split('/')?.filter(Boolean) ?? [] // remove empty parts
    // Find first region slug in the path
    let currentPageRegion: 'APAC' | 'EMEA' | 'AMER' = 'AMER'
    for (const segment of pathSegments) {
      if (SLUG_REGION_MAP[segment]) {
        currentPageRegion = SLUG_REGION_MAP[segment]
        break
      }
    }

    let countryCode, countryName, businessRegion;

    if (geolocationData?.countryCode) {
      // Use data from Redux if available
      countryCode = geolocationData.countryCode;
      countryName = geolocationData.countryName;
      businessRegion = geolocationData.businessRegion;
    } else {
      // Fetch from API if not in Redux
      const response = await fetch('/api/geo-location/')
      const data = await response.json()
      businessRegion = CONTINENT_REGION_MAP[data?.continent?.toUpperCase()] || 'AMER'
      countryCode = data?.country?.toUpperCase() || 'US'
      countryName = getCountryName(countryCode)
      // set the geo location data in the redux store
      dispatch(setGeoLocationData({
        countryCode,
        countryName,
        businessRegion,
        currentPageRegion,
      }))
    }

    const isForbury = process.env.NEXT_PUBLIC_DOMAIN === 'domainForburyCom'

    const isGeoNotificationPage = GEO_NOTIFICATION_SLUGS.some((slug) =>
      pathname?.includes(`${slug}/${!isForbury ? 'forbury' : ''}`)
    )

    // show the notification if the user is not in the correct region
    if (isGeoNotificationPage && currentPageRegion !== businessRegion) {
      // Create a consistent notification ID based on country and business region
      const notificationId = `geo-notification-${countryCode}-${businessRegion}-${currentPageRegion}::${uuid}`

      // store without uuid in session storage to check if the notification is dismissed
      const isDismissed = getSessionStorageItem(`dismissed_${notificationId?.split('::')?.[0] || ''}`)

      // Only show the notification if it hasn't been dismissed in this session
      if (!isDismissed) {
        dispatch(
          setLocalPushNotifications([
            {
              title: `We think you are in ${countryName}`,
              type: 'page',
              body: `Continue to our ${businessRegion} page for a better experience.`,
              url: `/solutions/${REGION_TO_SLUG_MAP[businessRegion || ""] || 'us'}/${!isForbury ? 'forbury/' : ''}`,
              timeStamp: new Date().toISOString(),
              duration: '0',
              icon: `https://flagsapi.com/${countryCode}/flat/64.png`,
              id: notificationId,
              domain: process.env.NEXT_PUBLIC_DOMAIN!,
              // domain: data?.country,
              ctaText: `Visit ${businessRegion} page`,
              category: JSON.stringify(['geolocation']),
              isSystemNotification: 'false', // Changed to 'true' so it uses system notification dismissal logic
              ctaTarget: '_self',
              isDismissable: 'true',
            },
          ])
        )
      }
    }
  }

  useEffect(() => {
    if (pathname) {
      fetchGeoLocation()
    }
  }, [pathname])

  return <></>
}

export default GeoLocationNotification

export function dismissGeoNotification() {
  // get the geo location data from the redux store 
  // @TODO : implement id based dismissal in future when integration with contentful happens
  const geolocationData = store?.getState()?.app?.geoLocationData
  const notificationId = `geo-notification-${geolocationData?.countryCode}-${geolocationData?.businessRegion}-${geolocationData?.currentPageRegion}`
  sessionStorage.setItem(`dismissed_${notificationId}`, 'true')
}