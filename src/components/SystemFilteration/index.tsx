'use client'
import { usePathname } from 'next/navigation'
import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { ICONS } from '../../globals/types'
import { useTranslation } from '../../globals/utils'
import useCopyFilterHook from '../../hooks/copyFilterHook'
import { useWindowSize } from '../../hooks/useWindowSize'
import { resetFilters } from '../../redux/slices/filterSlice/filtersSlice'
import { clearAllFilterEvent } from '../../utils/analyticsEvents'
import { Tag } from '../Bubbles'
import SimpleButtonWIcon from '../CTAs/SimpleButtonWIcon'
import LayoutContainer from '../Containers/LayoutContainer'
import SimpleHeading from '../ContentBlocks/Texts/Headings/SimpleHeading'
import MultiLevelDropdown from '../Controls/MultiLevelDropdown'
import MultiselectDropdown from '../Controls/MultiselectDropdown'
import Kernel from '../Kernel'
import ErrorToast from '../Notifications/Toasts/ErrorToast'
import { ErrorToastI } from '../Notifications/Toasts/ErrorToast/interface'
import SuccessToast from '../Notifications/Toasts/SuccessToast'
import { SuccessToastI } from '../Notifications/Toasts/SuccessToast/interface'
import SmallFiltration from './@Core/SmallFiltration'
import { SystemFiltrationD } from './defaults'
import style from './index.module.scss'
import { FIlterTagsData, FilterTags, SystemFiltrationI } from './interface'
import { getAllTags, getIcon, getSmallFilterationProps } from './utils'

export default function SystemFiltration(props: SystemFiltrationI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: SystemFiltrationI = {
    ...SystemFiltrationD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: props?.isAfsMap ? style.afsMapRoot : style.light,
      style: props.htmlAttr?.style,
    },
    button1: {
      ...SystemFiltrationD?.button1,
      textContent: useTranslation('clearFilters', props.locale),
      href: '#',
    },
    button2: {
      ...SystemFiltrationD?.button2,
      textContent: useTranslation('copyFilters', props.locale),
      href: '#',
    },
  }

  const buttons = {
    button1: updatedProps?.button1,
    button2: updatedProps?.button2,
  }

  // getting props for small filtration
  const smallFilteration = getSmallFilterationProps(updatedProps?.tabs)

  const [toastState, setToastState] = React.useState<boolean | string>(false)
  const [toastMessage, setToastMessage] = React.useState<
    SuccessToastI | ErrorToastI
  >({})

  const dispatch = useDispatch()
  const { size } = useWindowSize()

  const pathname = usePathname()

  useEffect(() => {
    console.log('pathname', pathname)

    dispatch(
      resetFilters({
        filterType: props?.filterType,
      })
    )
  }, [pathname])

  const tagsMap = useSelector(
    (state: any) => state?.tagsMap[`${props.filterType}TagsMap`]
  )

  const filterType: string = props?.filterType

  const filters = useSelector((state: any) => state?.filters)
  const filterTags = filters[filterType] || {}

  // function to copy the filter link
  const copyFilterHandler = () => {
    const filtersObject = filters[filterType]
    // if (!filtersObject || Object.keys(filtersObject)?.length <= 0) return
    const { copyFilterOnClickHandler } = useCopyFilterHook({
      filtersObject,
      setToastMessage,
      setToastState,
      locale: props.locale,
    })
    copyFilterOnClickHandler()
    setTimeout(() => {
      setToastState(false)
    }, 2000)
  }

  // function to clear all the filter
  const clearFilterHandler = () => {
    clearAllFilterEvent()
    //   const filtersObject = filters[filterType]
    // if (!filtersObject || Object.keys(filtersObject)?.length <= 0) return
    dispatch(
      resetFilters({
        filterType: props?.filterType,
      })
    )
    const { clearFilterOnClickHandler } = useCopyFilterHook({
      setToastMessage,
      setToastState,
      filterType,
    })
    clearFilterOnClickHandler()
    setTimeout(() => {
      setToastState(false)
    }, 2000)
  }

  // Toast Component for System Filteration Only
  const LocalToast = () => {
    if (toastState && toastState === 'success') {
      return (
        <div className={style.successToast}>
          <SuccessToast
            simpleParagraph={{ ...toastMessage }}
            timer={2000}
            autoHide
          />
        </div>
      )
    } else if (toastState && toastState === 'failure') {
      return (
        <div className={style.errorToast}>
          <ErrorToast
            simpleParagraph={{ ...toastMessage }}
            timer={2000}
            autoHide
          />
        </div>
      )
    } else {
      return null
    }
  }

  // function to get an array of tags as it was an object before
  const allTags = getAllTags(filterTags)

  function getTextContentsForFilterTags(
    filterTags: FilterTags,
    tags: FIlterTagsData[]
  ) {
    const textContents: any = []

    // Iterate over each key in filterTags
    Object.entries(filterTags).forEach(([key, valueIds]: any) => {
      // Find the tag that has a matching internalFilterField
      const matchingTag = tags.find(
        (tag: any) => tag.internalFilterField === key
      )
      if (matchingTag) {
        // Iterate over each id in valueIds
        valueIds.forEach((id: any) => {
          // Find the option in matchingTag that has the matching id
          const matchingOption = matchingTag.option.find(
            (option: any) => option.label.id === id
          )
          if (matchingOption) {
            // Push the textContent of the matching label to the results
            textContents.push(matchingOption.label.textContent)
          }
        })
      }
    })

    return textContents
  }

  // function to render the tags
  const tags = allTags.map((tag, ind) => {
    const modifiedTag = {
      ...tag,
      text: {
        ...tag.text,
        textContent:
          //   tagsMap?.[tag.id]?.split(':')[1] || splitByFirstCapital(tag.id)[1],
          getTextContentsForFilterTags(filterTags, updatedProps.tabs)[ind],
      },
    }
    return (
      <Tag key={ind} {...modifiedTag} filterType={filterType} size='small' />
    )
  })

  function getAfsMapIcon(index: number): ICONS {
    switch (index) {
      case 0:
        return 'Globe'
      case 1:
        return 'Buildings'
      case 2:
        return 'GeoAlt'
    }
  }

  const mapAfsComponent = (
    <Kernel {...updatedProps} isFullXBleed>
      <LayoutContainer>
        {<LocalToast />}
        <div className={style.container}>
          {updatedProps?.heading && (
            <SimpleHeading
              textContent={updatedProps?.heading}
              as='h4'
              colour='cs2'
            />
          )}
          <div className={style.upper}>
            {updatedProps.tabs?.map((tab, i) => {
              if (tab?.isMultilevel) {
                return (
                  <React.Fragment key={i}>
                    <MultiLevelDropdown
                      {...tab}
                      dropDownTab={{
                        ...tab?.dropDownTab,
                        fixedWidth: false,
                        hasBorder: true,
                        isIcon: true,
                        icon: {
                          ...tab?.dropDownTab?.icon,
                          icon: getIcon(
                            tab?.dropDownTab?.tabText.textContent as string
                          ),
                          iconColour: 'cn2',
                        },
                        htmlAttr: {
                          className: `${
                            updatedProps.tabs?.length <= 2
                              ? style.afsTwoTabs
                              : style.afsTab
                          }`,
                        },
                      }}
                    />
                  </React.Fragment>
                )
              } else {
                return (
                  <React.Fragment key={i}>
                    <MultiselectDropdown
                      {...tab}
                      dropDownTab={{
                        ...tab?.dropDownTab,
                        fixedWidth: false,
                        hasBorder: true,
                        isIcon: true,
                        icon: {
                          ...tab?.dropDownTab?.icon,
                          icon: getAfsMapIcon(i),
                          iconColour: 'cn2',
                        },
                        htmlAttr: {
                          className: ` ${
                            updatedProps.tabs?.length <= 2
                              ? style.afsTwoTabs
                              : style.afsTab
                          }`,
                        },
                      }}
                    />
                  </React.Fragment>
                )
              }
            })}
            <div className={style.buttons}>
              <SimpleButtonWIcon
                {...updatedProps.button1}
                htmlAttr={{
                  onClick: (e) => {
                    e.preventDefault()
                    clearFilterHandler()
                  },
                  filter: true,
                  className: style.afsCta,
                }}
              />
              <SimpleButtonWIcon
                {...updatedProps.button2}
                htmlAttr={{
                  onClick: (e) => {
                    e.preventDefault()
                    copyFilterHandler()
                  },
                  filter: true,
                  className: style.afsCta,
                }}
              />
            </div>
          </div>
          {tags?.length !== 0 && (
            <div className={style.lower}>
              <div className={style.tag}>{tags}</div>
            </div>
          )}
        </div>
      </LayoutContainer>
    </Kernel>
  )
  // main filteration component
  const component =
    size === 'small' ? (
      <SmallFiltration
        {...smallFilteration!}
        buttons={buttons}
        locale={props.locale}
      />
    ) : (
      <Kernel {...updatedProps} isFullXBleed>
        <LayoutContainer>
          {<LocalToast />}
          <div className={style.container}>
            <div className={style.upper}>
              {updatedProps.tabs?.map((tab, i) => {
                if (tab?.isMultilevel) {
                  return (
                    <React.Fragment key={i}>
                      <MultiLevelDropdown
                        {...tab}
                        dropDownTab={{
                          ...tab?.dropDownTab,
                          fixedWidth: false,
                          hasBorder: true,
                          isIcon: true,
                          icon: {
                            ...tab?.dropDownTab?.icon,
                            icon: getIcon(
                              tab?.dropDownTab?.tabText.textContent as string
                            ),
                            iconColour: 'cn2',
                          },
                          htmlAttr: {
                            className: `${
                              updatedProps.tabs?.length <= 2
                                ? style.twoTabs
                                : style.tab
                            }`,
                          },
                        }}
                      />
                    </React.Fragment>
                  )
                } else {
                  return (
                    <React.Fragment key={i}>
                      <MultiselectDropdown
                        {...tab}
                        dropDownTab={{
                          ...tab?.dropDownTab,
                          fixedWidth: false,
                          hasBorder: true,
                          isIcon: true,
                          icon: {
                            ...tab?.dropDownTab?.icon,
                            icon: getAfsMapIcon(i),
                            iconColour: 'cn2',
                          },
                          htmlAttr: {
                            className: ` ${
                              updatedProps.tabs?.length <= 2
                                ? style.twoTabs
                                : style.tab
                            }`,
                          },
                        }}
                      />
                    </React.Fragment>
                  )
                }
              })}
              <div className={style.buttons}>
                <SimpleButtonWIcon
                  {...updatedProps.button1}
                  htmlAttr={{
                    onClick: (e) => {
                      e.preventDefault()
                      clearFilterHandler()
                    },
                    filter: true,
                    className: style.cta,
                  }}
                />
                <SimpleButtonWIcon
                  {...updatedProps.button2}
                  htmlAttr={{
                    onClick: (e) => {
                      e.preventDefault()
                      copyFilterHandler()
                    },
                    filter: true,
                    className: style.cta,
                  }}
                />
              </div>
            </div>
            <div className={style.lower}>
              <div className={style.tag}>{tags}</div>
            </div>
          </div>
        </LayoutContainer>
      </Kernel>
    )

  return updatedProps?.isAfsMap ? mapAfsComponent : component
}
