'use client'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from '../../../../globals/utils'
import useCopyFilterHook from '../../../../hooks/copyFilterHook'
import {
  resetFilters,
  setCityFilters,
  setFilters,
} from '../../../../redux/slices/filterSlice/filtersSlice'
import SimpleAccordionItem from '../../../Accordions/SimpleAccordion/@Core/SImpleAccordionItem'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import LayoutContainer from '../../../Containers/LayoutContainer'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Checkbox from '../../../Controls/Checkbox'
import Kernel from '../../../Kernel'
import GenericIcon from '../../../Multimedia/Icons/SysIcon'
import ErrorToast from '../../../Notifications/Toasts/ErrorToast'
import { ErrorToastI } from '../../../Notifications/Toasts/ErrorToast/interface'
import SuccessToast from '../../../Notifications/Toasts/SuccessToast'
import { SuccessToastI } from '../../../Notifications/Toasts/SuccessToast/interface'
import { SmallFiltrationD } from './defaults'
import styles from './index.module.scss'
import { SmallFiltrationI } from './interface'

export default function SmallFiltration(props: SmallFiltrationI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  let updatedProps: SmallFiltrationI = {
    ...SmallFiltrationD,
    ...props,
  }

  updatedProps = {
    ...updatedProps,
    heading: {
      textContent: useTranslation('filters', props.locale),
    },
  }

  const [currentIndex, setCurrentIndex] = useState<number | null>(null)
  const [open, setOpen] = useState<boolean>(false)
  const [toastState, setToastState] = useState<boolean | string>(false)
  const [toastMessage, setToastMessage] = useState<SuccessToastI | ErrorToastI>(
    {}
  )

  const dispatch = useDispatch()

  const pathname = usePathname()

  useEffect(() => {
    console.log('pathname', pathname)
    dispatch(
      resetFilters({
        filterType: props?.filterType,
      })
    )
  }, [pathname])

  const filters = useSelector((state: any) => state.filters)
  const filterType = updatedProps?.accordionsMain[0]?.filterType

  //const background = `${open ? 'bn2' : 'bs2'}`

  // function to extract the checked items for a specific accordion tab
  const getCheckedItem = (index) => {
    const internalField = updatedProps?.accordionsMain[index]?.internalField
    const checkedItems =
      filterType &&
      internalField &&
      filters[filterType] &&
      filters[filterType][internalField]
        ? filters[filterType][internalField]
        : []
    return checkedItems
  }

  // function to extract the checked countries and cities for a specific multi-level accordion tab
  const getCheckedCountries = (index) => {
    const internalField = updatedProps?.accordionsMain[index]?.internalField
    const primaryField = updatedProps?.accordionsMain[index]?.primaryField
    const checkedCountries =
      filterType && internalField && filters[filterType] && primaryField
        ? filters[filterType][primaryField?.split(':')[1]]
        : []

    const checkedCities =
      filterType && internalField && filters[filterType]
        ? filters[filterType][primaryField]
        : []

    const countryAndCityOptions =
      updatedProps?.accordionsMain[index]?.countryAndCityOptions

    return {
      primaryField,
      checkedCountries,
      countryAndCityOptions,
      checkedCities,
    }
  }

  // simple handler to open and close accordion
  const AccordionHandler = () => {
    setOpen((pre) => !pre)
  }

  // function to handle checkbox change and updating redux state accordingly
  const handleCheckboxChange = (
    accordionItem: any,
    item: any,
    isChecked: any,
    primaryField: any
  ) => {
    if (isChecked) {
      dispatch(
        setFilters({
          filterType: accordionItem?.filterType,
          field: accordionItem?.internalField,
          value: item?.label?.id,
        })
      )
    } else {
      dispatch(
        setFilters({
          filterType: accordionItem?.filterType,
          field: accordionItem?.internalField,
          value: item?.label?.id,
          deleteCountry: true,
          primaryField: primaryField,
        })
      )
    }
  }

  // function to handle city checkbox change and updating redux state accordingly for multi-level accordions
  const handleCityCheckboxChange = (
    accordionItem?: any,
    item: any,
    countryName: any,
    i
  ) => {
    const { primaryField } = getCheckedCountries(i)
    dispatch(
      setCityFilters({
        filterType: accordionItem.filterType,
        primaryField: primaryField,
        value: item.label.id,
        countryName: countryName,
      })
    )
  }

  useEffect(() => {
    dispatch(
      resetFilters({
        filterType: filterType,
      })
    )
  }, [pathname])

  // function to copy the filter link
  const copyFilterHandler = () => {
    const filtersObject = filters[filterType]
    if (!filtersObject || Object.keys(filtersObject)?.length <= 0) return
    const { copyFilterOnClickHandler } = useCopyFilterHook({
      filtersObject,
      setToastMessage,
      setToastState,
      locale: props.locale,
    })
    copyFilterOnClickHandler()
    setTimeout(() => {
      setToastState(false)
    }, 2000)
  }

  // function to clear all the filter
  const clearFilterHandler = () => {
    const filtersObject = filters[filterType]
    if (!filtersObject || Object.keys(filtersObject)?.length <= 0) return
    dispatch(
      resetFilters({
        filterType: filterType,
      })
    )
    const { clearFilterOnClickHandler } = useCopyFilterHook({
      setToastMessage,
      setToastState,
      filterType,
    })
    clearFilterOnClickHandler()
    setTimeout(() => {
      setToastState(false)
    }, 2000)
  }

  // Toast Component for Small Filteration Only
  const LocalToast = () => {
    if (toastState && toastState === 'success') {
      return (
        <div className={styles.successToast}>
          <SuccessToast
            simpleParagraph={{ ...toastMessage }}
            timer={2000}
            autoHide
          />
        </div>
      )
    } else if (toastState && toastState === 'failure') {
      return (
        <div className={styles.errorToast}>
          <ErrorToast
            simpleParagraph={{ ...toastMessage }}
            timer={2000}
            autoHide
          />
        </div>
      )
    } else {
      return null
    }
  }

  // Accordion Component
  const accordion = updatedProps?.accordionsMain?.map((accordionItem, i) => {
    // getting the checked items for a specific accordion
    const checkedItems = getCheckedItem(i)

    // getting the checked countries and cities for a specific multi-level accordion
    const {
      primaryField,
      checkedCountries,
      countryAndCityOptions,
      checkedCities,
    } = getCheckedCountries(i)

    return (
      <SimpleAccordionItem
        key={i}
        htmlAttr={{
          className: `${styles.accordionContainer} `,
        }}
        acrTab={{
          ...accordionItem?.SimpleAccordion.acrTab,
          isBTop: false,
          isBBottom: true,
          isBLeft: false,
          isBRight: false,
          isRounded: false,
          isIcon: true,
          bColor: 'cn4',
          htmlAttr: {
            ...accordionItem?.SimpleAccordion.acrTab?.htmlAttr,
            className: `${styles.accordionHeader}`,
          },
          icon2: {
            ...accordionItem?.SimpleAccordion.acrTab?.icon2,
            iconColour: 'cn2',
          },
          heading: {
            ...accordionItem?.SimpleAccordion.acrTab?.heading,
            as: 'h6',
            colour: 'cn2',
            htmlAttr: {
              ...accordionItem?.SimpleAccordion.acrTab?.heading?.htmlAttr,
              className: 'fSansReg',
            },
          },

          icon: {
            ...accordionItem?.SimpleAccordion.acrTab?.icon,
            icon: 'DownChevron',
            iconColour: 'cn2',
          },
        }}
        acrTabC={{
          ...accordionItem?.SimpleAccordion.acrTabC,
          htmlAttr: {
            ...accordionItem?.SimpleAccordion.acrTabC?.htmlAttr,
            className: `${currentIndex === i && styles.bB} ${
              styles.accordionTabContent
            }`,
          },
        }}
        isOpen={currentIndex === i}
        index={i}
        isAutoClose={updatedProps.isAutoClose}
        isCloseable={updatedProps.isCloseable}
        currentIndex={currentIndex as number}
        setCurrentIndex={() => setCurrentIndex((pre) => (pre !== i ? i : null))}
        isLightMode={updatedProps.isLightMode}
      >
        {accordionItem?.checkbox?.map((item, checkboxIdx) => {
          return (
            <>
              <Checkbox
                key={checkboxIdx}
                {...item}
                isChecked={
                  checkedCountries?.includes(item?.label?.id as string) ||
                  checkedItems?.includes(item?.label?.id as string)
                }
                onChange={(isChecked: any) =>
                  handleCheckboxChange(
                    accordionItem,
                    item,
                    isChecked,
                    primaryField
                  )
                }
                htmlAttr={{
                  className: styles.checkbox,
                }}
              />
              {/* for multi-level */}
              {primaryField &&
                accordionItem?.internalField === primaryField?.split(':')[1] &&
                checkedCountries?.some((country: any) => {
                  return country === item?.label?.id
                }) &&
                countryAndCityOptions &&
                countryAndCityOptions[item?.label?.textContent]?.map(
                  (city, idx) => {
                    const checked = checkedCities?.some((countryObject) =>
                      Object.values(countryObject)
                        .flat()
                        .includes(city.label.id)
                    )
                    return (
                      <div
                        style={{ paddingLeft: '30px', listStyleType: 'none' }}
                      >
                        <Checkbox
                          htmlAttr={{
                            className: styles.checkbox,
                          }}
                          isChecked={checked}
                          key={idx}
                          {...city}
                          onChange={() =>
                            handleCityCheckboxChange(
                              accordionItem,
                              city,
                              item?.label?.id,
                              i
                            )
                          }
                        />
                      </div>
                    )
                  }
                )}
            </>
          )
        })}
      </SimpleAccordionItem>
    )
  })

  return (
    <Kernel
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        className: `${styles.RootClass}  ${updatedProps.htmlAttr?.className}`,
      }}
    >
      <div className={'bp1'}>
        <LayoutContainer>
          {<LocalToast />}
          <div className={styles.conatiner}>
            <div className={styles.left}>
              <GenericIcon
                {...updatedProps.leftIcon}
                icon='Filter'
                iconColour='cs2'
              />
              <SimpleHeading
                {...updatedProps.heading}
                as='h6'
                colour='cs2'
                fontFamily='fSansReg'
              />
            </div>
            <GenericIcon
              {...updatedProps.closeIcon}
              size={open ? 'md' : 'sm'}
              iconColour='cs2'
              icon={open ? 'Close' : 'DownChevron'}
              htmlAttr={{ onClick: AccordionHandler }}
            />
          </div>
        </LayoutContainer>
      </div>
      {open && (
        <div className={styles.tabs}>
          {' '}
          {accordion}
          <LayoutContainer>
            <div className={styles.links}>
              <SimpleButtonWIcon
                {...updatedProps.buttons.button1}
                isIconPrefixed
                htmlAttr={{
                  className: styles.cta,
                  onClick: (e) => {
                    e.preventDefault()
                    clearFilterHandler()
                  },
                }}
              />
              <SimpleButtonWIcon
                {...updatedProps.buttons.button2}
                isIconPrefixed
                htmlAttr={{
                  className: styles.cta,
                  onClick: (e) => {
                    e.preventDefault()
                    copyFilterHandler()
                  },
                }}
              />
            </div>
          </LayoutContainer>{' '}
          {/*     <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '20px',
                padding: '20px',
              }}
            >
              <SimpleButtonWIcon
                {...updatedProps.buttons.button1}
                htmlAttr={{
                  onClick: () => {
                    clearFilterOnClickHandler()
                  },
                }}
              />
              <SimpleButtonWIcon
                {...updatedProps.buttons.button2}
                htmlAttr={{
                  onClick: () => {
                    copyFilterOnClickHandler()
                  },
                }}
              />
            </div> */}
        </div>
      )}
    </Kernel>
  )
}
