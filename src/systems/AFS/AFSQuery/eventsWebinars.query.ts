import { linkFragment } from '../../../lib/queries/link.query'
// This query is not used in current setup please remove later
export const systemEventsAndWebinarsQuery = `
{
   systemAfs(preview: true, id: "1CmsYTWhx6phDruJRWfRYC") {
    __typename
    sys {
      id
    }
    template
    contentfulMetadata {
      __typename
      tags {
        id
        name
        __typename
      }
    }
    afsFiltersCollection {
      __typename
      items {
        heading
        template
        afsTagNames
        afsTagIds
        isMultiLevel
        isLightMode
      }
    }
  }
}
`

export const pageCollectionQuery = (tag: string) => `
{
  pageCollection(preview: true, where: {contentfulMetadata: {tags: {id_contains_all: "${tag}"}}}) {
    items {
      title
      shortTitle
      template
      contentfulMetadata {
        tags {
          id
          name
        }
      }
      slug
      seoTitle
      seoDescription
      afsCardTitle
      afsDescription
      seoKeywords
      publishDate
      pageThumbnail {
        url
      }
    }
  }
}
`

export const eventWebinarCardCollectionQuery = (tag: string) => `
{
  cardComponentCollection(preview: true, where: {contentfulMetadata: {tags: {id_contains_all: "${tag}"}}}) {
    items {
      sys {
      id    
    }
    contentfulMetadata {
        tags {
          id
        }
      }
    __typename
    internalName
    template
    isLightMode
    heading
    eventType
    subHeading
    description
    checklist
    startTime
    endTime
    timeZone
    number
    image {
      title
      url
    }
    address{
      altusL1
      altusL2
      altusCity
      altusState
      altusCountry
      altusUrl
    }
    button {
      ...on LinkComponent{
      ${linkFragment}
      }
    }
   buttonGroupCollection(limit: 2){
      items{
        ...on LinkComponent{
       ${linkFragment}
        }
      }
    }
    fieldMapping
    isSymlink
    fullName
    contentfulMetadata {
      tags {
        id
        name
      }
    }
    jobTitle
    companyName
    icon
    size
    publishDate
    }
  }
}
`
