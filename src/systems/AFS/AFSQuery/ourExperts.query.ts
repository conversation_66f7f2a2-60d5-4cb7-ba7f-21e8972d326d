import { linkFragment } from '../../../lib/queries/link.query'
// This query is not used in current setup please remove later
export const systemOurExpertsQuery = (
  id: string = '7Fek3PeuXYhjWsXCjK1C76',
  locale: string = 'en-CA'
) => `
{
   systemAfs(preview: true, id: "${id}", locale: "${locale}") {
    __typename
    sys {
      id
    }
    template
    contentfulMetadata {
      __typename
      tags {
        id
        name
        __typename
      }
    }
    afsFiltersCollection {
      __typename
      items {
        heading
        template
        afsTagNames
        afsTagIds
        isMultiLevel
        isLightMode
      }
    }
  }
}
`
// This query is not used in current setup please remove later
export const pageCollectionQuery = (tag: string) => `
{
  pageCollection(preview: true, where: {contentfulMetadata: {tags: {id_contains_all: "${tag}"}}}) {
    items {
      title
      shortTitle
      template
      contentfulMetadata {
        tags {
          id
          name
        }
      }
      slug
      seoTitle
      seoDescription
      afsCardTitle
      afsDescription
      seoKeywords
      publishDate
      pageThumbnail {
        url
      }
    }
  }
}
`
export const ourExpertsCardCollectionQuery = (tag: string) => `
{
  cardComponentCollection(preview: true, where: {contentfulMetadata: {tags: {id_contains_all: "${tag}"}}}) {
    items {
      sys {
      id    
    }
    __typename
    internalName
    template
    isLightMode
    heading
    eventType
    subHeading
    description
    checklist
    startTime
    endTime
    number
    image {
      title
      url
    }
    address{
      altusL1
      altusL2
      altusCity
      altusState
      altusCountry
      altusUrl
    }
    button {
      ...on LinkComponent{
      ${linkFragment}
      }
    }
   buttonGroupCollection(limit: 2){
      items{
        ...on LinkComponent{
       ${linkFragment}
        }
      }
    }
    fieldMapping
    isSymlink
    fullName
    contentfulMetadata {
      tags {
        id
        name
      }
    }
    jobTitle
    companyName
    icon
    size
    publishDate
    }
  }
}
`
