// This query is not used in current setup please remove later
export const systemCustomerStoriesQuery = (
  id: string = '7BmALKIov0yGsPoqxqbWtV',
  locale: string = 'en-CA'
) => `
{
   systemAfs(preview: true, id: "${id}", locale: "${locale}") {
    __typename
    sys {
      id
    }
    template
    contentfulMetadata {
      __typename
      tags {
        id
        name
        __typename
      }
    }
    afsFiltersCollection {
      __typename
      items {
        heading
        template
        afsTagNames
        afsTagIds
        isMultiLevel
        isLightMode
      }
    }
  }
}
`

export const pageCollectionQuery = (tag: string) => `
{
  pageCollection(preview: true, where: {contentfulMetadata: {tags: {id_contains_all: "${tag}"}}}) {
    items {
      title
      shortTitle
      template
      contentfulMetadata {
        tags {
          id
          name
        }
      }
      slug
      seoTitle
      seoDescription
      afsCardTitle
      afsDescription
      seoKeywords
      publishDate
      pageThumbnail {
        url
      }
    }
  }
}
`
